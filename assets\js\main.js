// Main JavaScript file for PornTubeX
class PornTubeX {
    constructor() {
        this.videos = [];
        this.users = [];
        this.comments = [];
        this.categories = [];
        this.favorites = this.getFavorites();
        this.likes = this.getLikes();
        
        this.init();
    }
    
    async init() {
        try {
            await this.loadData();
            this.setupEventListeners();
        } catch (error) {
            console.error('Failed to initialize PornTubeX:', error);
            this.showError('Failed to load content. Please refresh the page.');
        }
    }
    
    async loadData() {
        try {
            const [videosResponse, usersResponse, commentsResponse, categoriesResponse] = await Promise.all([
                fetch('data/videos.json'),
                fetch('data/users.json'),
                fetch('data/comments.json'),
                fetch('data/categories.json')
            ]);
            
            if (!videosResponse.ok || !usersResponse.ok || !commentsResponse.ok || !categoriesResponse.ok) {
                throw new Error('Failed to fetch data');
            }
            
            const videosData = await videosResponse.json();
            const usersData = await usersResponse.json();
            const commentsData = await commentsResponse.json();
            const categoriesData = await categoriesResponse.json();
            
            this.videos = videosData.videos;
            this.users = usersData.users;
            this.comments = commentsData.comments;
            this.categories = categoriesData.categories;
            
        } catch (error) {
            console.error('Error loading data:', error);
            throw error;
        }
    }
    
    setupEventListeners() {
        // Search functionality
        const searchForm = document.getElementById('searchForm');
        const searchInput = document.getElementById('searchInput');
        
        if (searchForm && searchInput) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performSearch(searchInput.value.trim());
            });
            
            // Real-time search suggestions (optional)
            searchInput.addEventListener('input', (e) => {
                const query = e.target.value.trim();
                if (query.length > 2) {
                    this.showSearchSuggestions(query);
                }
            });
        }
        
        // Modal functionality
        this.setupModalListeners();
    }
    
    setupModalListeners() {
        // Close modals when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target);
            }
        });
        
        // Close modals with close button
        document.querySelectorAll('.modal-close').forEach(button => {
            button.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                this.closeModal(modal);
            });
        });
        
        // Escape key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const activeModal = document.querySelector('.modal.active');
                if (activeModal) {
                    this.closeModal(activeModal);
                }
            }
        });
    }
    
    performSearch(query) {
        if (!query) return;
        
        const searchResults = this.videos.filter(video => 
            video.title.toLowerCase().includes(query.toLowerCase()) ||
            video.description.toLowerCase().includes(query.toLowerCase()) ||
            video.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase())) ||
            video.uploader.username.toLowerCase().includes(query.toLowerCase())
        );
        
        // Redirect to search results page or update current page
        this.displaySearchResults(searchResults, query);
    }
    
    displaySearchResults(results, query) {
        // For now, we'll update the current page if it has a videos grid
        const videosGrid = document.getElementById('featuredVideos') || 
                          document.getElementById('recentVideos') || 
                          document.getElementById('categoryVideos');
        
        if (videosGrid) {
            videosGrid.innerHTML = '';
            
            if (results.length === 0) {
                videosGrid.innerHTML = `
                    <div class="no-results">
                        <h3>No results found for "${query}"</h3>
                        <p>Try different keywords or browse our categories.</p>
                    </div>
                `;
                return;
            }
            
            results.forEach(video => {
                const videoCard = this.createVideoCard(video);
                videosGrid.appendChild(videoCard);
            });
            
            // Update page title if possible
            const sectionTitle = document.querySelector('.section-title');
            if (sectionTitle) {
                sectionTitle.textContent = `Search Results for "${query}" (${results.length})`;
            }
        }
    }
    
    createVideoCard(video) {
        const videoCard = document.createElement('a');
        videoCard.className = 'video-card';
        videoCard.href = `pages/video.html?id=${video.id}`;
        
        const isLiked = this.likes.includes(video.id);
        const isFavorited = this.favorites.includes(video.id);
        
        videoCard.innerHTML = `
            <div class="video-thumbnail">
                <img src="${video.thumbnail}" alt="${video.title}" loading="lazy">
                <span class="video-duration">${video.duration}</span>
            </div>
            <div class="video-info">
                <h3 class="video-title">${video.title}</h3>
                <div class="video-meta">
                    <img src="${video.uploader.avatar}" alt="${video.uploader.username}" class="uploader-avatar">
                    <a href="#" class="uploader-name">${video.uploader.username}</a>
                </div>
                <div class="video-stats">
                    <span class="views">${this.formatNumber(video.views)} views</span>
                    <span class="likes">${this.formatNumber(video.likes)} likes</span>
                    <span class="rating">★ ${video.rating}</span>
                </div>
            </div>
        `;
        
        return videoCard;
    }
    
    createCategoryCard(category) {
        const categoryCard = document.createElement('a');
        categoryCard.className = 'category-card';
        categoryCard.href = `pages/category.html?category=${category.id}`;
        
        categoryCard.innerHTML = `
            <div class="category-icon">${category.icon}</div>
            <h3 class="category-name">${category.name}</h3>
            <p class="category-description">${category.description}</p>
        `;
        
        return categoryCard;
    }
    
    // Utility functions
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) {
            return 'Yesterday';
        } else if (diffDays < 7) {
            return `${diffDays} days ago`;
        } else if (diffDays < 30) {
            const weeks = Math.floor(diffDays / 7);
            return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
        } else if (diffDays < 365) {
            const months = Math.floor(diffDays / 30);
            return `${months} month${months > 1 ? 's' : ''} ago`;
        } else {
            const years = Math.floor(diffDays / 365);
            return `${years} year${years > 1 ? 's' : ''} ago`;
        }
    }
    
    // LocalStorage functions
    getFavorites() {
        const favorites = localStorage.getItem('pornTubeX_favorites');
        return favorites ? JSON.parse(favorites) : [];
    }
    
    saveFavorites() {
        localStorage.setItem('pornTubeX_favorites', JSON.stringify(this.favorites));
    }
    
    getLikes() {
        const likes = localStorage.getItem('pornTubeX_likes');
        return likes ? JSON.parse(likes) : [];
    }
    
    saveLikes() {
        localStorage.setItem('pornTubeX_likes', JSON.stringify(this.likes));
    }
    
    toggleFavorite(videoId) {
        const index = this.favorites.indexOf(videoId);
        if (index > -1) {
            this.favorites.splice(index, 1);
        } else {
            this.favorites.push(videoId);
        }
        this.saveFavorites();
        return this.favorites.includes(videoId);
    }
    
    toggleLike(videoId) {
        const index = this.likes.indexOf(videoId);
        if (index > -1) {
            this.likes.splice(index, 1);
        } else {
            this.likes.push(videoId);
        }
        this.saveLikes();
        return this.likes.includes(videoId);
    }
    
    // Modal functions
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }
    
    closeModal(modal) {
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    }
    
    // Error handling
    showError(message) {
        console.error(message);
        // You could implement a toast notification system here
        alert(message);
    }
    
    // Get video by ID
    getVideoById(id) {
        return this.videos.find(video => video.id === id);
    }
    
    // Get user by ID
    getUserById(id) {
        return this.users.find(user => user.id === id);
    }
    
    // Get comments for video
    getCommentsForVideo(videoId) {
        return this.comments.filter(comment => comment.videoId === videoId);
    }
    
    // Get category by ID
    getCategoryById(id) {
        return this.categories.find(category => category.id === id);
    }
    
    // Get videos by category
    getVideosByCategory(categoryId) {
        return this.videos.filter(video => video.category === categoryId);
    }
    
    // Get related videos (by tags and category)
    getRelatedVideos(video, limit = 6) {
        const related = this.videos.filter(v => {
            if (v.id === video.id) return false;
            
            // Check if videos share tags or category
            const sharedTags = v.tags.some(tag => video.tags.includes(tag));
            const sameCategory = v.category === video.category;
            
            return sharedTags || sameCategory;
        });
        
        // Sort by relevance (more shared tags = higher relevance)
        related.sort((a, b) => {
            const aSharedTags = a.tags.filter(tag => video.tags.includes(tag)).length;
            const bSharedTags = b.tags.filter(tag => video.tags.includes(tag)).length;
            return bSharedTags - aSharedTags;
        });
        
        return related.slice(0, limit);
    }
}

// Initialize the application
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new PornTubeX();
});
